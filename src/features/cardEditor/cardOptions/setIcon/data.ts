import { SetIcon } from '@cardEditor/cardOptions/setIcon';
import {
  sunAndMoon as smBaseSet,
  swordAndShield as ssBaseSet,
  scarletAndViolet as svBaseSet,
} from '../baseSet';

let id = 1;

export const promo: SetIcon = {
  id: id++,
  slug: 'promo',
  displayName: 'Promo',
  shape: 'square',
};

export const swordAndShield: SetIcon = {
  id: id++,
  slug: 'swordAndShield',
  displayName: 'Base',
  baseSet: ssBaseSet.id,
  shape: 'square',
};

export const rebelClash: SetIcon = {
  id: id++,
  slug: 'rebelClash',
  displayName: 'Rebel Clash',
  baseSet: ssBaseSet.id,
  shape: 'square',
};

export const darknessAblaze: SetIcon = {
  id: id++,
  slug: 'darknessAblaze',
  displayName: 'Darkness Ablaze',
  baseSet: ssBaseSet.id,
  shape: 'square',
};

export const futsal: SetIcon = {
  id: id++,
  slug: 'futsal',
  displayName: 'Futsal Collection',
  baseSet: ssBaseSet.id,
  shape: 'square',
};

export const championsPath: SetIcon = {
  id: id++,
  slug: 'championsPath',
  displayName: 'Champions Path',
  baseSet: ssBaseSet.id,
  shape: 'square',
};

export const vividVoltage: SetIcon = {
  id: id++,
  slug: 'vividVoltage',
  displayName: 'Vivid Voltage',
  baseSet: ssBaseSet.id,
  shape: 'square',
};

export const shiningFates: SetIcon = {
  id: id++,
  slug: 'shiningFates',
  displayName: 'Shining Fates',
  baseSet: ssBaseSet.id,
  shape: 'square',
};

export const battleStyles: SetIcon = {
  id: id++,
  slug: 'battleStyles',
  displayName: 'Battle Styles',
  baseSet: ssBaseSet.id,
  shape: 'square',
};

export const chillingReign: SetIcon = {
  id: id++,
  slug: 'chillingReign',
  displayName: 'Chilling Reign',
  baseSet: ssBaseSet.id,
  shape: 'square',
};

export const evolvingSkies: SetIcon = {
  id: id++,
  slug: 'evolvingSkies',
  displayName: 'Evolving Skies',
  baseSet: ssBaseSet.id,
  shape: 'square',
};

export const mcDonalds2021: SetIcon = {
  id: id++,
  slug: 'mcDonalds2021',
  displayName: "McDonald's Collection 2021",
  baseSet: ssBaseSet.id,
  shape: 'rectangle',
};

export const celebrations: SetIcon = {
  id: id++,
  slug: 'celebrations',
  displayName: 'Celebrations',
  baseSet: ssBaseSet.id,
  shape: 'square',
};

export const fusionStrike: SetIcon = {
  id: id++,
  slug: 'fusionStrike',
  displayName: 'Fusion Strike',
  baseSet: ssBaseSet.id,
  shape: 'square',
};

export const brilliantStars: SetIcon = {
  id: id++,
  slug: 'brilliantStars',
  displayName: 'Brilliant Stars',
  baseSet: ssBaseSet.id,
  shape: 'square',
};

export const astralRadiance: SetIcon = {
  id: id++,
  slug: 'astralRadiance',
  displayName: 'Astral Radiance',
  baseSet: ssBaseSet.id,
  shape: 'square',
};

export const pokemonGO: SetIcon = {
  id: id++,
  slug: 'pokemonGO',
  displayName: 'Pokémon GO',
  baseSet: ssBaseSet.id,
  shape: 'square',
};

export const lostOrigin: SetIcon = {
  id: id++,
  slug: 'lostOrigin',
  displayName: 'Lost Origin',
  baseSet: ssBaseSet.id,
  shape: 'square',
};

export const sunAndMoon: SetIcon = {
  id: id++,
  slug: 'sunAndMoon',
  displayName: 'Base',
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const guardiansRising: SetIcon = {
  id: id++,
  slug: 'guardiansRising',
  displayName: 'Guardians Rising',
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const burningShadows: SetIcon = {
  id: id++,
  slug: 'burningShadows',
  displayName: 'Burning Shadows',
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const shiningLegends: SetIcon = {
  id: id++,
  slug: 'shiningLegends',
  displayName: 'Shining Legends',
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const crimsonInvasion: SetIcon = {
  id: id++,
  slug: 'crimsonInvasion',
  displayName: 'Crimson Invasion',
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const mcDonalds2017: SetIcon = {
  id: id++,
  slug: 'mcDonalds2017',
  displayName: "McDonald's Collection 2017",
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const ultraPrism: SetIcon = {
  id: id++,
  slug: 'ultraPrism',
  displayName: 'Ultra Prism',
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const forbiddenLight: SetIcon = {
  id: id++,
  slug: 'forbiddenLight',
  displayName: 'Forbidden Light',
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const celestialStorm: SetIcon = {
  id: id++,
  slug: 'celestialStorm',
  displayName: 'Celestial Storm',
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const dragonMajesty: SetIcon = {
  id: id++,
  slug: 'dragonMajesty',
  displayName: 'Dragon Majesty',
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const mcDonalds2018: SetIcon = {
  id: id++,
  slug: 'mcDonalds2018',
  displayName: "McDonald's Collection 2018",
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const lostThunder: SetIcon = {
  id: id++,
  slug: 'lostThunder',
  displayName: 'Lost Thunder',
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const teamUp: SetIcon = {
  id: id++,
  slug: 'teamUp',
  displayName: 'Team Up',
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const detectivePikachu: SetIcon = {
  id: id++,
  slug: 'detectivePikachu',
  displayName: 'Detective Pikachu',
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const unbrokenBonds: SetIcon = {
  id: id++,
  slug: 'unbrokenBonds',
  displayName: 'Unbroken Bonds',
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const unifiedMinds: SetIcon = {
  id: id++,
  slug: 'unifiedMinds',
  displayName: 'Unified Minds',
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const hiddenFates: SetIcon = {
  id: id++,
  slug: 'hiddenFates',
  displayName: 'Hidden Fates',
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const mcDonalds2019: SetIcon = {
  id: id++,
  slug: 'mcDonalds2019',
  displayName: "McDonald's Collection 2019",
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const cosmicEclipse: SetIcon = {
  id: id++,
  slug: 'cosmicEclipse',
  displayName: 'Cosmic Eclipse',
  baseSet: smBaseSet.id,
  shape: 'square',
};

export const scarletAndViolet: SetIcon = {
  id: id++,
  slug: 'scarletAndViolet',
  displayName: 'Scarlet & Violet',
  baseSet: svBaseSet.id,
  shape: 'setRectangle',
};

export const setIcons: SetIcon[] = [
  promo,
  scarletAndViolet,
  swordAndShield,
  rebelClash,
  darknessAblaze,
  futsal,
  championsPath,
  vividVoltage,
  shiningFates,
  battleStyles,
  chillingReign,
  evolvingSkies,
  mcDonalds2021,
  celebrations,
  fusionStrike,
  brilliantStars,
  astralRadiance,
  pokemonGO,
  lostOrigin,
  sunAndMoon,
  guardiansRising,
  burningShadows,
  shiningLegends,
  crimsonInvasion,
  mcDonalds2017,
  ultraPrism,
  forbiddenLight,
  celestialStorm,
  dragonMajesty,
  mcDonalds2018,
  lostThunder,
  teamUp,
  detectivePikachu,
  unbrokenBonds,
  unifiedMinds,
  hiddenFates,
  mcDonalds2019,
  cosmicEclipse,
];
