export enum AnalyticsEvent {
  ThemeSwitch = 'theme_switch',
}

export enum CardCreatorAnalyticsEvent {
  BaseSetChange = 'base_set_change',
  SupertypeChange = 'supertype_change',
  TypeChange = 'type_change',
  SubtypeChange = 'subtype_change',
  VariationChange = 'variation_change',
  RarityChange = 'rarity_change',
  BadgeIconChange = 'badge_icon_change',
  WeaknessTypeChange = 'weakness_type_change',
  ResistanceTypeChange = 'resistance_type_change',
  RetreatCostChange = 'retreat_cost_change',
  SetIconChange = 'set_icon_change',
  RotationIconChange = 'rotation_icon_change',
  RarityIconChange = 'rarity_icon_change',
  CardDownload = 'card_download',
  CardShare = 'card_share',
}

export enum ConsentCookie {
  Analytics = 'analytics_consent',
}
