import { Paper, Typography } from '@mui/material';
import { FC } from 'react';

const Footer: FC = () => (
  <Paper
    component="footer"
    sx={{
      p: [8, undefined, 1],
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 0,
    }}
  >
    <Typography variant="h6" align="center">
      © {new Date().getFullYear()} Pokecardmaker.org
    </Typography>
    {process.env.NEXT_PUBLIC_ENVIRONMENT !== 'production' && (
      <Typography
        variant="h6"
        align="center"
        fontWeight="bold"
        textTransform="uppercase"
        ml={2}
      >
        {process.env.NEXT_PUBLIC_ENVIRONMENT}
      </Typography>
    )}
  </Paper>
);

export default Footer;
