{"compilerOptions": {"baseUrl": ".", "lib": ["dom", "dom.iterable", "esnext"], "paths": {"@cardEditor": ["src/features/cardEditor"], "@cardEditor/*": ["src/features/cardEditor/*"], "@features/*": ["src/features/*"], "@components/*": ["src/components/*"], "@hooks/*": ["src/hooks/*"], "@utils/*": ["src/utils/*"], "@css": ["src/css"], "@routes": ["src/routes"], "@interfaces/*": ["src/interfaces/*"], "@layout": ["src/layout"], "@assets/*": ["public/assets/*"]}, "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "target": "es5", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "emitDecoratorMetadata": true, "incremental": false}, "include": ["next-env-custom.d.ts", "**/*.ts", "**/*.tsx", "**/*.js"], "exclude": ["node_modules", "dist"]}